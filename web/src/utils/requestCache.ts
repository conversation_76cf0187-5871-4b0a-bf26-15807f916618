// Request caching and deduplication utility
export class RequestCache {
  private static instance: RequestCache;
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  private ongoingRequests = new Map<string, Promise<any>>();

  private constructor() {}

  static getInstance(): RequestCache {
    if (!RequestCache.instance) {
      RequestCache.instance = new RequestCache();
    }
    return RequestCache.instance;
  }

  // Get cached data if it exists and is not expired
  get<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    const now = Date.now();
    if (now - cached.timestamp > cached.ttl) {
      this.cache.delete(key);
      return null;
    }

    return cached.data as T;
  }

  // Set cached data with TTL (time to live in milliseconds)
  set<T>(key: string, data: T, ttl: number = 5 * 60 * 1000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  // Execute a request with deduplication and caching
  async execute<T>(
    key: string,
    requestFn: () => Promise<T>,
    ttl: number = 5 * 60 * 1000,
    useCache: boolean = true
  ): Promise<T> {
    // Check cache first
    if (useCache) {
      const cached = this.get<T>(key);
      if (cached) {
        console.log(`Cache hit for key: ${key}`);
        return cached;
      }
    }

    // Check if request is already in progress
    if (this.ongoingRequests.has(key)) {
      console.log(`Request already in progress for key: ${key}`);
      return this.ongoingRequests.get(key) as Promise<T>;
    }

    // Execute the request
    console.log(`Executing new request for key: ${key}`);
    const requestPromise = requestFn()
      .then((result) => {
        // Cache the result
        if (useCache) {
          this.set(key, result, ttl);
        }
        // Clean up ongoing request
        this.ongoingRequests.delete(key);
        return result;
      })
      .catch((error) => {
        // Clean up ongoing request on error
        this.ongoingRequests.delete(key);
        throw error;
      });

    // Store the ongoing request
    this.ongoingRequests.set(key, requestPromise);

    return requestPromise;
  }

  // Clear cache for a specific key
  clear(key: string): void {
    this.cache.delete(key);
    this.ongoingRequests.delete(key);
  }

  // Clear all cache
  clearAll(): void {
    this.cache.clear();
    this.ongoingRequests.clear();
  }

  // Get cache statistics
  getStats(): { cacheSize: number; ongoingRequests: number } {
    return {
      cacheSize: this.cache.size,
      ongoingRequests: this.ongoingRequests.size
    };
  }
}

// Export singleton instance
export const requestCache = RequestCache.getInstance();

// Utility function to create cache keys
export const createCacheKey = (prefix: string, ...params: (string | number)[]): string => {
  return `${prefix}:${params.join(':')}`;
};
