// API request debugging utility
export class APIDebugger {
  private static instance: APIDebugger;
  private requests: Map<string, { count: number; lastCall: number; stack?: string }> = new Map();
  private enabled: boolean = process.env.NODE_ENV === 'development';

  private constructor() {}

  static getInstance(): APIDebugger {
    if (!APIDebugger.instance) {
      APIDebugger.instance = new APIDebugger();
    }
    return APIDebugger.instance;
  }

  // Log an API request
  logRequest(url: string, method: string = 'GET'): void {
    if (!this.enabled) return;

    const key = `${method} ${url}`;
    const now = Date.now();
    const existing = this.requests.get(key);

    if (existing) {
      existing.count++;
      existing.lastCall = now;
      
      // Warn about frequent requests
      if (existing.count > 3) {
        console.warn(`🚨 Frequent API calls detected: ${key} (${existing.count} times)`);
        console.warn('Stack trace:', new Error().stack);
      }
    } else {
      this.requests.set(key, {
        count: 1,
        lastCall: now,
        stack: new Error().stack
      });
    }

    console.log(`📡 API Request: ${key} (${existing ? existing.count : 1} times)`);
  }

  // Get request statistics
  getStats(): Array<{ url: string; count: number; lastCall: Date }> {
    return Array.from(this.requests.entries()).map(([url, data]) => ({
      url,
      count: data.count,
      lastCall: new Date(data.lastCall)
    }));
  }

  // Clear statistics
  clear(): void {
    this.requests.clear();
  }

  // Enable/disable debugging
  setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }

  // Check for duplicate requests within a time window
  checkDuplicates(windowMs: number = 1000): Array<{ url: string; count: number }> {
    const now = Date.now();
    const duplicates: Array<{ url: string; count: number }> = [];

    for (const [url, data] of this.requests.entries()) {
      if (now - data.lastCall <= windowMs && data.count > 1) {
        duplicates.push({ url, count: data.count });
      }
    }

    return duplicates;
  }

  // Print summary
  printSummary(): void {
    if (!this.enabled) return;

    console.group('📊 API Request Summary');
    
    const stats = this.getStats();
    const duplicates = this.checkDuplicates(5000); // 5 second window

    console.log('Total unique requests:', stats.length);
    console.log('Total requests:', stats.reduce((sum, stat) => sum + stat.count, 0));

    if (duplicates.length > 0) {
      console.warn('🚨 Potential duplicate requests:');
      duplicates.forEach(dup => {
        console.warn(`  ${dup.url}: ${dup.count} times`);
      });
    }

    console.log('Most frequent requests:');
    stats
      .sort((a, b) => b.count - a.count)
      .slice(0, 5)
      .forEach(stat => {
        console.log(`  ${stat.url}: ${stat.count} times (last: ${stat.lastCall.toLocaleTimeString()})`);
      });

    console.groupEnd();
  }
}

// Export singleton instance
export const apiDebugger = APIDebugger.getInstance();

// Monkey patch fetch to automatically log requests
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  const originalFetch = window.fetch;
  
  window.fetch = function(input: RequestInfo | URL, init?: RequestInit): Promise<Response> {
    const url = typeof input === 'string' ? input : input instanceof URL ? input.toString() : input.url;
    const method = init?.method || 'GET';
    
    // Only log API requests (not static assets)
    if (url.includes('/api/') || url.includes('/chats/')) {
      apiDebugger.logRequest(url, method);
    }
    
    return originalFetch.call(this, input, init);
  };

  // Print summary every 30 seconds
  setInterval(() => {
    apiDebugger.printSummary();
  }, 30000);
}

// Utility to manually log requests (for server-side or when fetch patching doesn't work)
export const logAPIRequest = (url: string, method: string = 'GET'): void => {
  apiDebugger.logRequest(url, method);
};
