import { useCallback, useEffect, useRef } from 'react';
import { useChatStore, useAuthStore } from '@/lib/stores';
import {
  getChatList,
  getChatById,
  createNewChat,
  updateChatById,
  deleteChatById,
  getAllTags,
  addTagById,
  deleteTagById,
  deleteTagsById
} from '@/lib/api/chats';
import type { Chat } from '@/lib/types';
import { toast } from 'sonner';

export const useChats = () => {
  const { token } = useAuthStore();
  const {
    chats,
    currentChat,
    isLoading,
    error,
    setChats,
    addChat,
    updateChat,
    deleteChat,
    setCurrentChat,
    setLoading,
    setError
  } = useChatStore();

  // Request deduplication - track ongoing requests
  const ongoingRequests = useRef<Map<string, Promise<any>>>(new Map());

  // Load chats
  const loadChats = useCallback(async (page: number | null = null) => {
    if (!token) return;

    setLoading(true);
    try {
      const response = await getChatList(token, page);
      setChats(response.data || []);
    } catch (error: any) {
      console.error('Failed to load chats:', error);
      setError(error.message || 'Failed to load chats');
    } finally {
      setLoading(false);
    }
  }, [token, setChats, setLoading, setError]);

  // Load chat by ID with caching and request deduplication
  const loadChat = useCallback(async (chatId: string, forceReload: boolean = false) => {
    if (!token || !chatId || chatId.trim() === '') {
      console.warn('Cannot load chat: missing token or chatId');
      return null;
    }

    const requestKey = `loadChat-${chatId}`;

    // Check if we already have this chat loaded and it's not a forced reload
    if (!forceReload && currentChat?.id === chatId) {
      console.log('Chat already loaded in store, returning cached version:', chatId);
      return currentChat;
    }

    // Check if there's already an ongoing request for this chat
    if (ongoingRequests.current.has(requestKey)) {
      console.log('Request already in progress for chat:', chatId);
      return ongoingRequests.current.get(requestKey);
    }

    try {
      console.log('Loading chat from API:', chatId);

      // Create and store the request promise
      const requestPromise = getChatById(token, chatId).then((chat) => {
        setCurrentChat(chat);
        ongoingRequests.current.delete(requestKey); // Clean up
        return chat;
      }).catch((error) => {
        ongoingRequests.current.delete(requestKey); // Clean up on error
        throw error;
      });

      ongoingRequests.current.set(requestKey, requestPromise);

      return await requestPromise;
    } catch (error: any) {
      console.error('Failed to load chat:', error);
      setError(error.message || 'Failed to load chat');
      return null;
    }
  }, [token, currentChat, setCurrentChat, setError]);

  // Create new chat
  const createChat = useCallback(async (chatData: any) => {
    if (!token) return null;

    try {
      const newChat = await createNewChat(token, chatData);
      addChat(newChat);
      setCurrentChat(newChat);
      return newChat;
    } catch (error: any) {
      console.error('Failed to create chat:', error);
      const errorMessage = error.message || 'Failed to create chat';
      setError(errorMessage);
      toast.error(errorMessage);
      return null;
    }
  }, [token, addChat, setCurrentChat, setError]);

  // Update chat
  const updateChatData = useCallback(async (chatId: string, updates: Partial<Chat>) => {
    if (!token) return null;

    try {
      const updatedChat = await updateChatById(token, chatId, updates);
      updateChat(chatId, updatedChat);
      
      if (currentChat?.id === chatId) {
        setCurrentChat(updatedChat);
      }
      
      return updatedChat;
    } catch (error: any) {
      console.error('Failed to update chat:', error);
      const errorMessage = error.message || 'Failed to update chat';
      setError(errorMessage);
      toast.error(errorMessage);
      return null;
    }
  }, [token, updateChat, currentChat, setCurrentChat, setError]);

  // Delete chat
  const removeChatData = useCallback(async (chatId: string) => {
    if (!token) return false;

    try {
      await deleteChatById(token, chatId);
      deleteChat(chatId);
      
      if (currentChat?.id === chatId) {
        setCurrentChat(null);
      }
      
      toast.success('Chat deleted');
      return true;
    } catch (error: any) {
      console.error('Failed to delete chat:', error);
      const errorMessage = error.message || 'Failed to delete chat';
      setError(errorMessage);
      toast.error(errorMessage);
      return false;
    }
  }, [token, deleteChat, currentChat, setCurrentChat, setError]);

  // Load tags
  const loadTags = useCallback(async () => {
    if (!token) return [];

    try {
      const tags = await getAllTags(token);
      return tags;
    } catch (error: any) {
      console.error('Failed to load tags:', error);
      return [];
    }
  }, [token]);

  // Add tag to chat
  const addChatTag = useCallback(async (chatId: string, tagName: string) => {
    if (!token) return false;

    try {
      await addTagById(token, chatId, tagName);
      
      // Update local chat data
      const chat = chats.find(c => c.id === chatId);
      if (chat) {
        const updatedTags = [...(chat.tags || []), tagName];
        updateChat(chatId, { ...chat, tags: updatedTags });
      }
      
      if (currentChat?.id === chatId) {
        const updatedTags = [...(currentChat.tags || []), tagName];
        setCurrentChat({ ...currentChat, tags: updatedTags });
      }
      
      toast.success('Tag added');
      return true;
    } catch (error: any) {
      console.error('Failed to add tag:', error);
      const errorMessage = error.message || 'Failed to add tag';
      toast.error(errorMessage);
      return false;
    }
  }, [token, chats, currentChat, updateChat, setCurrentChat]);

  // Remove tag from chat
  const removeChatTag = useCallback(async (chatId: string, tagName: string) => {
    if (!token) return false;

    try {
      await deleteTagById(token, chatId, tagName);
      
      // Update local chat data
      const chat = chats.find(c => c.id === chatId);
      if (chat) {
        const updatedTags = (chat.tags || []).filter(tag => tag !== tagName);
        updateChat(chatId, { ...chat, tags: updatedTags });
      }
      
      if (currentChat?.id === chatId) {
        const updatedTags = (currentChat.tags || []).filter(tag => tag !== tagName);
        setCurrentChat({ ...currentChat, tags: updatedTags });
      }
      
      toast.success('Tag removed');
      return true;
    } catch (error: any) {
      console.error('Failed to remove tag:', error);
      const errorMessage = error.message || 'Failed to remove tag';
      toast.error(errorMessage);
      return false;
    }
  }, [token, chats, currentChat, updateChat, setCurrentChat]);

  // Remove all tags from chat
  const removeAllChatTags = useCallback(async (chatId: string) => {
    if (!token) return false;

    try {
      await deleteTagsById(token, chatId);
      
      // Update local chat data
      const chat = chats.find(c => c.id === chatId);
      if (chat) {
        updateChat(chatId, { ...chat, tags: [] });
      }
      
      if (currentChat?.id === chatId) {
        setCurrentChat({ ...currentChat, tags: [] });
      }
      
      toast.success('All tags removed');
      return true;
    } catch (error: any) {
      console.error('Failed to remove all tags:', error);
      const errorMessage = error.message || 'Failed to remove all tags';
      toast.error(errorMessage);
      return false;
    }
  }, [token, chats, currentChat, updateChat, setCurrentChat]);

  // Initialize chats on mount
  useEffect(() => {
    if (token && !isLoading && chats.length === 0) {
      loadChats();
    }
  }, [token, loadChats, isLoading, chats.length]);

  return {
    // State
    chats,
    currentChat,
    isLoading,
    error,

    // Actions
    loadChats,
    loadChat,
    createChat,
    updateChatData,
    removeChatData,
    loadTags,
    addChatTag,
    removeChatTag,
    removeAllChatTags,
    setCurrentChat,
  };
};
